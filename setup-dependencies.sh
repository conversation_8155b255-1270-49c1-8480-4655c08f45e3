#!/bin/bash

# Setup Dependencies for N8N Update Automation
# This script installs required dependencies for the n8n update automation

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Detect OS
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if [ -f /etc/debian_version ]; then
            echo "debian"
        elif [ -f /etc/redhat-release ]; then
            echo "redhat"
        else
            echo "linux"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    else
        echo "unknown"
    fi
}

# Install dependencies based on OS
install_dependencies() {
    local os=$(detect_os)
    
    print_status "Detected OS: $os"
    print_status "Installing required dependencies..."
    
    case $os in
        "debian")
            print_status "Installing packages for Debian/Ubuntu..."
            sudo apt-get update
            sudo apt-get install -y sshpass openssh-client curl
            ;;
        "redhat")
            print_status "Installing packages for CentOS/RHEL..."
            sudo yum install -y sshpass openssh-clients curl
            ;;
        "macos")
            print_status "Installing packages for macOS..."
            if ! command -v brew &> /dev/null; then
                print_error "Homebrew is not installed. Please install it first:"
                echo "Visit: https://brew.sh/"
                exit 1
            fi
            brew install sshpass
            ;;
        *)
            print_error "Unsupported operating system: $os"
            print_status "Please install the following packages manually:"
            echo "  - sshpass"
            echo "  - openssh-client (or openssh-clients)"
            echo "  - curl"
            exit 1
            ;;
    esac
}

# Verify installation
verify_installation() {
    print_status "Verifying installation..."
    
    local missing_deps=()
    
    if ! command -v sshpass &> /dev/null; then
        missing_deps+=("sshpass")
    fi
    
    if ! command -v ssh &> /dev/null; then
        missing_deps+=("ssh")
    fi
    
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi
    
    if [ ${#missing_deps[@]} -eq 0 ]; then
        print_success "All dependencies are installed successfully!"
        return 0
    else
        print_error "Missing dependencies: ${missing_deps[*]}"
        return 1
    fi
}

# Main function
main() {
    echo "=================================="
    echo "  N8N Update Dependencies Setup"
    echo "=================================="
    echo ""
    
    # Check if running as root (not recommended)
    if [ "$EUID" -eq 0 ]; then
        print_warning "Running as root. This is not recommended for security reasons."
        echo -n "Continue anyway? (y/N): "
        read -r confirmation
        case $confirmation in
            [yY]|[yY][eE][sS])
                ;;
            *)
                print_status "Setup cancelled"
                exit 1
                ;;
        esac
    fi
    
    # Install dependencies
    install_dependencies
    
    # Verify installation
    if verify_installation; then
        echo ""
        print_success "Setup completed successfully!"
        print_status "You can now run the n8n update automation script:"
        echo "  ./n8n-update-automation.sh"
    else
        echo ""
        print_error "Setup failed. Please install missing dependencies manually."
        exit 1
    fi
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
