# N8N Update Guide

## Overview
This guide provides both automated and manual processes for updating your n8n Docker instance running on server `**************`.

## Prerequisites

### Required Software
- `sshpass` for automated SSH connections
- `ssh` client
- `curl` (for verification)

### Installation Commands
```bash
# Ubuntu/Debian
sudo apt-get update && sudo apt-get install sshpass openssh-client curl

# CentOS/RHEL
sudo yum install sshpass openssh-clients curl

# macOS
brew install sshpass
```

## Automated Update Process

### Quick Start
```bash
# Make script executable (if not already done)
chmod +x n8n-update-automation.sh

# Run the automation script
./n8n-update-automation.sh
```

### Script Features
- **Interactive Menu**: User-friendly text interface
- **Connection Testing**: Verify SSH connectivity before operations
- **Automated Backup**: Creates timestamped backups of container and data
- **Update Process**: Pulls latest n8n image and recreates container
- **Verification**: Checks container status and web interface
- **Rollback Capability**: Restore from backup if update fails
- **Comprehensive Logging**: All operations logged with timestamps
- **Error Handling**: Graceful error handling with clear messages

### Menu Options
1. **Test SSH Connection** - Verify connectivity to server
2. **Check Current N8N Status** - View container status and version
3. **Create Backup Only** - Create backup without updating
4. **Full Update Process** - Complete backup, update, and verification
5. **Rollback to Last Backup** - Restore previous version
6. **View Update Log** - Show recent log entries
7. **Exit** - Close the application

## Manual Update Process

### Phase 1: Connect and Prepare

#### Step 1: SSH Connection
```bash
ssh root@**************
# Password: Netflix$1000
```

#### Step 2: Check Current Status
```bash
# Check running containers
docker ps -a --filter name=n8n

# Check current n8n version
docker inspect n8n --format='{{.Config.Image}}'

# Check container logs
docker logs n8n --tail 20
```

### Phase 2: Create Backup

#### Step 3: Create Backup Directory
```bash
mkdir -p /root/n8n-backups
cd /root/n8n-backups
```

#### Step 4: Create Container Backup
```bash
# Create timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="n8n_backup_$TIMESTAMP"

# Stop container gracefully
docker stop n8n

# Create container image backup
docker commit n8n $BACKUP_NAME

# Export container backup
docker save $BACKUP_NAME > ${BACKUP_NAME}.tar

# Backup data volume
docker run --rm -v n8n_data:/data -v /root/n8n-backups:/backup alpine \
  tar czf /backup/${BACKUP_NAME}_data.tar.gz -C /data .

# Restart container
docker start n8n
```

### Phase 3: Update Process

#### Step 5: Stop and Remove Current Container
```bash
# Stop the container
docker stop n8n

# Remove container (keeps volumes)
docker rm n8n
```

#### Step 6: Pull Latest Image
```bash
# Pull latest n8n image
docker pull n8nio/n8n:latest
```

#### Step 7: Start Updated Container
```bash
# Start new container with same configuration
docker run -d --name n8n --restart unless-stopped \
  -p 5678:5678 \
  -v n8n_data:/home/<USER>/.n8n \
  -e N8N_HOST=0.0.0.0 \
  -e N8N_PORT=5678 \
  -e WEBHOOK_URL=http://**************:5678/ \
  n8nio/n8n:latest
```

### Phase 4: Verification

#### Step 8: Verify Update
```bash
# Check container status
docker ps --filter name=n8n

# Check logs for errors
docker logs n8n --tail 20

# Test web interface
curl -I http://localhost:5678

# Check new version
docker inspect n8n --format='{{.Config.Image}}'
```

#### Step 9: Test Functionality
1. Open browser to `http://**************:5678`
2. Verify login works
3. Check existing workflows
4. Test a simple workflow execution

## Rollback Process (If Needed)

### Emergency Rollback
```bash
# Stop current container
docker stop n8n && docker rm n8n

# Load backup image
docker load < /root/n8n-backups/n8n_backup_TIMESTAMP.tar

# Start backup container
docker run -d --name n8n --restart unless-stopped \
  -p 5678:5678 \
  -v n8n_data:/home/<USER>/.n8n \
  -e N8N_HOST=0.0.0.0 \
  -e N8N_PORT=5678 \
  -e WEBHOOK_URL=http://**************:5678/ \
  n8n_backup_TIMESTAMP

# Restore data volume if needed
docker run --rm -v n8n_data:/data -v /root/n8n-backups:/backup alpine \
  sh -c 'rm -rf /data/* && tar xzf /backup/n8n_backup_TIMESTAMP_data.tar.gz -C /data'
```

## Security Considerations

### Best Practices
- Change default SSH password after setup
- Use SSH keys instead of passwords for production
- Regularly update Docker and host system
- Monitor backup storage space
- Test rollback procedures periodically

### Script Security Features
- Credentials are configurable at script top
- SSH strict host key checking disabled for automation
- All operations logged for audit trail
- Confirmation prompts for destructive operations

## Troubleshooting

### Common Issues
1. **SSH Connection Failed**: Check network connectivity and credentials
2. **Container Won't Start**: Check Docker logs and port conflicts
3. **Web Interface Not Accessible**: Verify firewall and port settings
4. **Data Loss**: Use rollback procedure to restore from backup

### Log Locations
- Script logs: `/tmp/n8n-update-TIMESTAMP.log`
- Docker logs: `docker logs n8n`
- System logs: `/var/log/syslog` or `journalctl -u docker`

## Support
For issues with this automation script, check:
1. Script log files for detailed error messages
2. Docker container logs
3. Network connectivity to server
4. Available disk space for backups

Remember to test the update process in a development environment before running on production!
