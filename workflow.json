{"name": "ROBO-RESEARCHER-2000 Complete 17-Step Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "robo-researcher", "responseMode": "responseNode", "options": {"allowedOrigins": "*"}}, "id": "webhook-trigger", "name": "1. <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "webhookId": "robo-researcher"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "project-name-check", "leftValue": "={{ $json.projectName }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "email-check", "leftValue": "={{ $json.email }}", "rightValue": "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$", "operator": {"type": "string", "operation": "regex"}}, {"id": "transcription-check", "leftValue": "={{ $json.transcription?.length }}", "rightValue": 100, "operator": {"type": "number", "operation": "gte"}}], "combinator": "and"}, "options": {}}, "id": "validate-input", "name": "2. Validate Input", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"assignments": {"assignments": [{"id": "execution-id", "name": "executionId", "value": "=exec_{{ $now.format('YYYYMMDD_HHmmss') }}", "type": "string"}, {"id": "project-name", "name": "projectName", "value": "={{ $json.projectName }}", "type": "string"}, {"id": "email", "name": "email", "value": "={{ $json.email }}", "type": "string"}, {"id": "transcription", "name": "transcription", "value": "={{ $json.transcription }}", "type": "string"}, {"id": "study-type", "name": "studyType", "value": "={{ $json.studyType || 'user_interview' }}", "type": "string"}, {"id": "language", "name": "language", "value": "={{ $json.language || 'en' }}", "type": "string"}, {"id": "timestamp", "name": "timestamp", "value": "={{ $now.toISO() }}", "type": "string"}, {"id": "step", "name": "step", "value": "validation_complete", "type": "string"}]}, "options": {}}, "id": "prepare-data", "name": "2b. Prepare Data", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [680, 240]}, {"parameters": {"operation": "upload", "bucketName": "={{ $vars.MINIO_BUCKET || 'robo-researcher-data' }}", "fileName": "={{ $json.executionId }}/original-transcription.txt", "binaryData": false, "fileContent": "={{ $json.transcription }}"}, "id": "upload-to-minio", "name": "3. Upload to MinIO", "type": "n8n-nodes-base.s3", "typeVersion": 1, "position": [900, 240], "credentials": {"s3": {"id": "minio-s3-credentials", "name": "MinIO S3 Storage"}}}, {"parameters": {"assignments": {"assignments": [{"id": "cleaned-text", "name": "cleanedTranscription", "value": "={{ $json.transcription.replace(/\\[.*?\\]/g, '').replace(/\\s+/g, ' ').trim() }}", "type": "string"}, {"id": "word-count", "name": "wordCount", "value": "={{ $json.transcription.split(' ').length }}", "type": "number"}, {"id": "preprocessing-complete", "name": "step", "value": "preprocessing_complete", "type": "string"}]}, "options": {}}, "id": "text-preprocessing", "name": "4. Text Preprocessing", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1120, 240]}, {"parameters": {"assignments": {"assignments": [{"id": "segments", "name": "segments", "value": "={{ $json.cleanedTranscription.split(/\\n\\n|\\. (?=[A-Z])/).map((segment, index) => ({ id: index + 1, text: segment.trim(), wordCount: segment.split(' ').length })).filter(seg => seg.text.length > 50) }}", "type": "object"}, {"id": "segment-count", "name": "segmentCount", "value": "={{ $json.segments?.length || 0 }}", "type": "number"}, {"id": "segmentation-complete", "name": "step", "value": "segmentation_complete", "type": "string"}]}, "options": {}}, "id": "segmentation", "name": "5. Segmentation", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1340, 240]}, {"parameters": {"assignments": {"assignments": [{"id": "deductive-codes", "name": "deductiveCodes", "value": "={{ $json.segments.map(segment => ({ segmentId: segment.id, codes: ['usability', 'navigation', 'feature_request', 'satisfaction', 'frustration'].filter(code => segment.text.toLowerCase().includes(code.replace('_', ' '))) })) }}", "type": "object"}, {"id": "coded-segments", "name": "codedSegments", "value": "={{ $json.segments.map((segment, index) => ({ ...segment, deductiveCodes: $json.deductiveCodes[index]?.codes || [] })) }}", "type": "object"}, {"id": "deductive-coding-complete", "name": "step", "value": "deductive_coding_complete", "type": "string"}]}, "options": {}}, "id": "deductive-coding", "name": "6. Deductive Coding", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1560, 240]}, {"parameters": {"model": "gpt-4o-mini", "messages": {"values": [{"content": "=Analyze the following user research segments and identify emergent themes and codes. Focus on patterns not captured by standard UX codes.\n\nSegments to analyze:\n{{ JSON.stringify($json.codedSegments.slice(0, 10), null, 2) }}\n\nFor each segment, identify:\n1. Emergent themes not covered by existing codes\n2. Specific user behaviors mentioned\n3. Emotional indicators\n4. Context-specific issues\n\nReturn a JSON array of emergent codes with format:\n[{\"code\": \"code_name\", \"description\": \"what this represents\", \"segments\": [segment_ids], \"frequency\": number}]"}]}, "options": {"temperature": 0.3, "maxTokens": 2000}}, "id": "open-coding-ai", "name": "7. Open Coding AI", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [1780, 240], "credentials": {"openAiApi": {"id": "openrouter-api", "name": "OpenRouter API"}}}, {"parameters": {"assignments": {"assignments": [{"id": "emergent-codes", "name": "emergentCodes", "value": "={{ JSON.parse($json.choices[0].message.content) }}", "type": "object"}, {"id": "all-codes", "name": "allCodes", "value": "={{ $('6. Deductive Coding').item.json.codedSegments.concat($json.emergentCodes || []) }}", "type": "object"}, {"id": "open-coding-complete", "name": "step", "value": "open_coding_complete", "type": "string"}]}, "options": {}}, "id": "merge-codes", "name": "7b. Merge Codes", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2000, 240]}, {"parameters": {"model": "gpt-4o-mini", "messages": {"values": [{"content": "=Organize the following codes into hierarchical categories for UX research analysis.\n\nCodes to categorize:\n{{ JSON.stringify($json.allCodes, null, 2) }}\n\nCreate categories with subcategories following this structure:\n{\n  \"user_experience\": {\n    \"name\": \"User Experience\",\n    \"subcategories\": {\n      \"usability\": {\"name\": \"Usability Issues\", \"codes\": []},\n      \"satisfaction\": {\"name\": \"User Satisfaction\", \"codes\": []}\n    }\n  },\n  \"product_features\": {\n    \"name\": \"Product Features\",\n    \"subcategories\": {\n      \"functionality\": {\"name\": \"Feature Functionality\", \"codes\": []},\n      \"requests\": {\"name\": \"Feature Requests\", \"codes\": []}\n    }\n  }\n}\n\nReturn organized categories with codes properly assigned."}]}, "options": {"temperature": 0.2, "maxTokens": 3000}}, "id": "category-grouping", "name": "8. Category Grouping", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [2220, 240], "credentials": {"openAiApi": {"id": "openrouter-api", "name": "OpenRouter API"}}}, {"parameters": {"model": "gpt-4o-mini", "messages": {"values": [{"content": "=Create an affinity map from the categorized codes to identify relationships and clusters.\n\nCategorized Codes:\n{{ JSON.stringify($json.choices[0].message.content, null, 2) }}\n\nGenerate an affinity map with:\n1. Clusters of related themes\n2. Relationships between clusters (strength: weak/moderate/strong)\n3. Insights about theme connections\n\nReturn JSON format:\n{\n  \"clusters\": [{\"id\": \"cluster_1\", \"name\": \"Cluster Name\", \"themes\": [], \"strength\": 0.8}],\n  \"relationships\": [{\"from\": \"cluster_1\", \"to\": \"cluster_2\", \"strength\": 0.6, \"type\": \"moderate\"}],\n  \"insights\": [{\"type\": \"connection\", \"description\": \"insight description\"}]\n}"}]}, "options": {"temperature": 0.3, "maxTokens": 3000}}, "id": "affinity-mapping", "name": "9. Affinity Mapping", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [2440, 240], "credentials": {"openAiApi": {"id": "openrouter-api", "name": "OpenRouter API"}}}, {"parameters": {"assignments": {"assignments": [{"id": "code-frequency", "name": "codeFrequency", "value": "={{ Object.fromEntries($json.allCodes.map(code => [code.code || 'unknown', ($json.allCodes.filter(c => c.code === code.code).length)])) }}", "type": "object"}, {"id": "total-codes", "name": "totalCodes", "value": "={{ $json.allCodes.length }}", "type": "number"}, {"id": "unique-codes", "name": "uniqueCodes", "value": "={{ Object.keys($json.codeFrequency).length }}", "type": "number"}, {"id": "most-frequent", "name": "mostFrequentCode", "value": "={{ Object.entries($json.codeFrequency).sort(([,a], [,b]) => b - a)[0] }}", "type": "object"}, {"id": "quantitative-complete", "name": "step", "value": "quantitative_analysis_complete", "type": "string"}]}, "options": {}}, "id": "quantitative-analysis", "name": "10. Quantitative Analysis", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2660, 240]}, {"parameters": {"model": "gpt-4o-mini", "messages": {"values": [{"content": "=Analyze the data to detect behavioral patterns and user journey insights.\n\nQuantitative Analysis:\n{{ JSON.stringify($json, null, 2) }}\n\nAffinity Map:\n{{ JSON.stringify($('9. Affinity Mapping').item.json.choices[0].message.content, null, 2) }}\n\nIdentify:\n1. Behavioral patterns in user interactions\n2. User journey pain points\n3. Recurring themes across segments\n4. Emotional journey patterns\n\nFor each pattern provide:\n- Pattern name and description\n- Supporting evidence\n- Frequency/prevalence\n- Impact assessment (high/medium/low)\n- Recommended actions\n\nReturn JSON array of detected patterns."}]}, "options": {"temperature": 0.3, "maxTokens": 4000}}, "id": "pattern-detection", "name": "11. <PERSON><PERSON> Detection", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [2880, 240], "credentials": {"openAiApi": {"id": "openrouter-api", "name": "OpenRouter API"}}}, {"parameters": {"model": "gpt-4o-mini", "messages": {"values": [{"content": "=Generate structured UX insights from the comprehensive analysis.\n\nDetected Patterns:\n{{ JSON.stringify($json.choices[0].message.content, null, 2) }}\n\nQuantitative Data:\n{{ JSON.stringify($('10. Quantitative Analysis').item.json, null, 2) }}\n\nGenerate actionable insights with:\n1. Key Findings (3-5 main discoveries)\n2. User Pain Points (prioritized with severity)\n3. Opportunities (specific improvement areas)\n4. Recommendations (actionable next steps)\n5. Success Metrics (measurement criteria)\n\nFor each insight include:\n- Clear description\n- Supporting evidence\n- Business impact assessment\n- Implementation complexity (low/medium/high)\n- Priority level (critical/high/medium/low)\n\nReturn structured JSON with insights."}]}, "options": {"temperature": 0.2, "maxTokens": 4000}}, "id": "insight-generation", "name": "12. Insight Generation", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [3100, 240], "credentials": {"openAiApi": {"id": "openrouter-api", "name": "OpenRouter API"}}}, {"parameters": {"model": "gpt-4o-mini", "messages": {"values": [{"content": "=Create data-driven user archetypes based on the analysis.\n\nStructured Insights:\n{{ JSON.stringify($json.choices[0].message.content, null, 2) }}\n\nDetected Patterns:\n{{ JSON.stringify($('11. Pattern Detection').item.json.choices[0].message.content, null, 2) }}\n\nCreate 3-5 user archetypes representing distinct user groups based on:\n1. Behavioral patterns\n2. Pain points and frustrations\n3. Goals and motivations\n4. Technology comfort level\n5. Usage context\n\nFor each archetype provide:\n- Name (memorable and descriptive)\n- Demographics (age range, role, experience)\n- Goals (what they want to achieve)\n- Frustrations (main pain points)\n- Behaviors (interaction patterns)\n- Motivations (what drives them)\n- Technology Comfort (novice/intermediate/expert)\n- Quote (representative statement)\n- Percentage (estimated user base portion)\n\nReturn JSON array of user archetypes."}]}, "options": {"temperature": 0.3, "maxTokens": 4000}}, "id": "archetype-creation", "name": "13. Archetype Creation", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [3320, 240], "credentials": {"openAiApi": {"id": "openrouter-api", "name": "OpenRouter API"}}}, {"parameters": {"model": "gpt-4o-mini", "messages": {"values": [{"content": "=Generate \"How Might We\" questions from the insights and user archetypes.\n\nUser Archetypes:\n{{ JSON.stringify($json.choices[0].message.content, null, 2) }}\n\nStructured Insights:\n{{ JSON.stringify($('12. Insight Generation').item.json.choices[0].message.content, null, 2) }}\n\nGenerate HMW questions that:\n1. <PERSON><PERSON> identified pain points\n2. Leverage opportunities\n3. Consider different user archetypes\n4. Focus on actionable solutions\n\nFor each HMW question provide:\n- Question text\n- Related pain point/opportunity\n- Target archetype(s)\n- Potential impact (high/medium/low)\n- Implementation complexity\n- Category (usability/features/design/etc)\n\nReturn JSON array of HMW questions grouped by category."}]}, "options": {"temperature": 0.4, "maxTokens": 3000}}, "id": "hmw-generation", "name": "14. HMW Generation", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [3540, 240], "credentials": {"openAiApi": {"id": "openrouter-api", "name": "OpenRouter API"}}}, {"parameters": {"model": "gpt-4o-mini", "messages": {"values": [{"content": "=Prioritize opportunities using RICE methodology (Reach, Impact, Confidence, Effort).\n\nHMW Questions:\n{{ JSON.stringify($json.choices[0].message.content, null, 2) }}\n\nInsights:\n{{ JSON.stringify($('12. Insight Generation').item.json.choices[0].message.content, null, 2) }}\n\nFor each opportunity/HMW question, score:\n- Reach: How many users affected (1-10)\n- Impact: Improvement magnitude (1-10)\n- Confidence: How sure we are (1-10)\n- Effort: Implementation effort (1-10, lower = less effort)\n\nCalculate RICE score: (Reach × Impact × Confidence) / Effort\n\nReturn prioritized opportunities with:\n- Opportunity description\n- RICE scores breakdown\n- Total RICE score\n- Priority ranking\n- Recommended timeline\n- Success metrics\n\nSort by RICE score (highest first)."}]}, "options": {"temperature": 0.2, "maxTokens": 3000}}, "id": "opportunity-prioritization", "name": "15. Opportunity Prioritization", "type": "n8n-nodes-base.openAi", "typeVersion": 1.4, "position": [3760, 240], "credentials": {"openAiApi": {"id": "openrouter-api", "name": "OpenRouter API"}}}, {"parameters": {"method": "POST", "url": "={{ $vars.WIKIJS_URL || 'http://localhost:3002' }}/api/pages", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "httpHeaderAuth": {"name": "Authorization", "value": "=Bearer {{ $vars.WIKIJS_API_TOKEN }}"}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "title", "value": "={{ $json.projectName }} - UX Research Analysis"}, {"name": "path", "value": "=/ux-research/{{ $json.executionId }}"}, {"name": "content", "value": "=# {{ $json.projectName }} - UX Research Analysis\n\n## Executive Summary\n\n**Project:** {{ $json.projectName }}\n**Analysis Date:** {{ $json.timestamp }}\n**Execution ID:** {{ $json.executionId }}\n\n## Key Findings\n\n{{ JSON.stringify($('12. Insight Generation').item.json.choices[0].message.content, null, 2) }}\n\n## User Archetypes\n\n{{ JSON.stringify($('13. Archetype Creation').item.json.choices[0].message.content, null, 2) }}\n\n## Prioritized Opportunities\n\n{{ JSON.stringify($('15. Opportunity Prioritization').item.json.choices[0].message.content, null, 2) }}\n\n## How Might We Questions\n\n{{ JSON.stringify($('14. HMW Generation').item.json.choices[0].message.content, null, 2) }}\n\n## Quantitative Analysis\n\n{{ JSON.stringify($('10. Quantitative Analysis').item.json, null, 2) }}\n\n## Methodology\n\nThis analysis was conducted using the ROBO-RESEARCHER-2000 automated UX research platform, following a comprehensive 17-step methodology:\n\n1. Data validation and preprocessing\n2. Text segmentation\n3. Deductive and emergent coding\n4. Category grouping and affinity mapping\n5. Quantitative analysis\n6. Pattern detection\n7. Insight generation\n8. User archetype creation\n9. Opportunity identification and prioritization\n\n---\n\n*Generated automatically by ROBO-RESEARCHER-2000*"}, {"name": "tags", "value": "=[\"ux-research\", \"{{ $json.projectName }}\", \"automated-analysis\"]"}]}, "options": {}}, "id": "wiki-documentation", "name": "16. Wiki Documentation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [3980, 240]}, {"parameters": {"fromEmail": "={{ $vars.SMTP_USER || '<EMAIL>' }}", "toEmail": "={{ $json.email }}", "subject": "=UX Research Analysis Complete: {{ $json.projectName }}", "emailFormat": "html", "message": "=<!DOCTYPE html>\n<html>\n<head>\n    <style>\n        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n        .header { background: #4CAF50; color: white; padding: 20px; text-align: center; }\n        .content { padding: 20px; }\n        .summary { background: #f9f9f9; padding: 15px; border-left: 4px solid #4CAF50; margin: 20px 0; }\n        .metrics { display: flex; justify-content: space-around; margin: 20px 0; }\n        .metric { text-align: center; }\n        .metric h3 { margin: 0; color: #4CAF50; }\n        .footer { background: #f1f1f1; padding: 15px; text-align: center; font-size: 12px; }\n        .button { background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0; }\n    </style>\n</head>\n<body>\n    <div class=\"header\">\n        <h1>🤖 ROBO-RESEARCHER-2000</h1>\n        <h2>UX Research Analysis Complete</h2>\n    </div>\n    \n    <div class=\"content\">\n        <h2>Project: {{ $json.projectName }}</h2>\n        \n        <div class=\"summary\">\n            <h3>📊 Analysis Summary</h3>\n            <p><strong>Execution ID:</strong> {{ $json.executionId }}</p>\n            <p><strong>Completed:</strong> {{ $json.timestamp }}</p>\n            <p><strong>Total Segments Analyzed:</strong> {{ $json.segmentCount || 0 }}</p>\n            <p><strong>Codes Identified:</strong> {{ $json.totalCodes || 0 }}</p>\n        </div>\n        \n        <h3>🎯 Top Insights</h3>\n        <div class=\"summary\">\n            <p>Your UX research analysis has identified key user pain points, behavioral patterns, and improvement opportunities. The analysis includes data-driven user archetypes and prioritized recommendations using the RICE methodology.</p>\n        </div>\n        \n        <h3>📚 Access Your Results</h3>\n        <p>Your complete analysis is available in the documentation system:</p>\n        <a href=\"{{ $vars.WIKIJS_URL || 'http://localhost:3002' }}/ux-research/{{ $json.executionId }}\" class=\"button\">View Complete Analysis</a>\n        \n        <h3>📈 Next Steps</h3>\n        <ol>\n            <li>Review the prioritized opportunities in your documentation</li>\n            <li>Share user archetypes with your design team</li>\n            <li>Use HMW questions for ideation sessions</li>\n            <li>Implement high-priority recommendations</li>\n            <li>Set up success metrics tracking</li>\n        </ol>\n    </div>\n    \n    <div class=\"footer\">\n        <p>Generated by ROBO-RESEARCHER-2000 | Automated UX Research Platform</p>\n        <p>For support, visit our documentation or contact your system administrator.</p>\n    </div>\n</body>\n</html>", "options": {}}, "id": "email-notification", "name": "17. Email Notification", "type": "n8n-nodes-base.emailSend", "typeVersion": 2.1, "position": [4200, 240], "credentials": {"smtp": {"id": "smtp-credentials", "name": "SMTP Email"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ { success: true, executionId: $json.executionId, projectName: $json.projectName, completionTime: $now.toISO(), message: 'UX Research analysis completed successfully for project: ' + $json.projectName, documentationUrl: ($vars.WIKIJS_URL || 'http://localhost:3002') + '/ux-research/' + $json.executionId } }}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [4420, 240]}], "connections": {"1. Webhook Trigger": {"main": [[{"node": "2. Validate Input", "type": "main", "index": 0}]]}, "2. Validate Input": {"main": [[{"node": "2b. Prepare Data", "type": "main", "index": 0}]]}, "2b. Prepare Data": {"main": [[{"node": "3. Upload to MinIO", "type": "main", "index": 0}]]}, "3. Upload to MinIO": {"main": [[{"node": "4. Text Preprocessing", "type": "main", "index": 0}]]}, "4. Text Preprocessing": {"main": [[{"node": "5. Segmentation", "type": "main", "index": 0}]]}, "5. Segmentation": {"main": [[{"node": "6. Deductive Coding", "type": "main", "index": 0}]]}, "6. Deductive Coding": {"main": [[{"node": "7. Open Coding AI", "type": "main", "index": 0}]]}, "7. Open Coding AI": {"main": [[{"node": "7b. Merge Codes", "type": "main", "index": 0}]]}, "7b. Merge Codes": {"main": [[{"node": "8. Category Grouping", "type": "main", "index": 0}]]}, "8. Category Grouping": {"main": [[{"node": "9. Affinity Mapping", "type": "main", "index": 0}]]}, "9. Affinity Mapping": {"main": [[{"node": "10. Quantitative Analysis", "type": "main", "index": 0}]]}, "10. Quantitative Analysis": {"main": [[{"node": "11. <PERSON><PERSON> Detection", "type": "main", "index": 0}]]}, "11. Pattern Detection": {"main": [[{"node": "12. Insight Generation", "type": "main", "index": 0}]]}, "12. Insight Generation": {"main": [[{"node": "13. Archetype Creation", "type": "main", "index": 0}]]}, "13. Archetype Creation": {"main": [[{"node": "14. HMW Generation", "type": "main", "index": 0}]]}, "14. HMW Generation": {"main": [[{"node": "15. Opportunity Prioritization", "type": "main", "index": 0}]]}, "15. Opportunity Prioritization": {"main": [[{"node": "16. Wiki Documentation", "type": "main", "index": 0}]]}, "16. Wiki Documentation": {"main": [[{"node": "17. Email Notification", "type": "main", "index": 0}]]}, "17. Email Notification": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "", "timezone": "America/New_York"}, "versionId": "2025.4", "meta": {"templateCredsSetupCompleted": false, "instanceId": "robo-researcher-2000"}, "id": "robo-researcher-complete-17step", "tags": [{"id": "robo-researcher", "name": "robo-researcher"}, {"id": "ux-research", "name": "ux-research"}, {"id": "production", "name": "production"}, {"id": "complete", "name": "complete"}]}