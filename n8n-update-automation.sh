#!/bin/bash

# N8N Docker Update Automation Script
# Author: <PERSON>
# Description: Automated script to update n8n Docker container on remote server
# Version: 1.0

# Configuration
SERVER_IP="**************"
SERVER_USER="root"
SERVER_PASSWORD="Netflix\$1000"
N8N_CONTAINER_NAME="n8n"
BACKUP_DIR="/root/n8n-backups"
LOG_FILE="/tmp/n8n-update-$(date +%Y%m%d_%H%M%S).log"
N8N_TARGET_VERSION="latest"  # Default to latest, can be overridden

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
    log "INFO: $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    log "SUCCESS: $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    log "WARNING: $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    log "ERROR: $1"
}

# Progress indicator
show_progress() {
    local duration=$1
    local message=$2
    echo -n "$message"
    for ((i=0; i<duration; i++)); do
        echo -n "."
        sleep 1
    done
    echo " Done!"
}

# SSH command execution function
execute_remote_command() {
    local command=$1
    local description=$2
    
    print_status "$description"
    
    sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" "$command" 2>&1 | tee -a "$LOG_FILE"
    local exit_code=${PIPESTATUS[0]}
    
    if [ $exit_code -eq 0 ]; then
        print_success "$description completed successfully"
        return 0
    else
        print_error "$description failed with exit code $exit_code"
        return $exit_code
    fi
}

# Test SSH connection
test_connection() {
    print_status "Testing SSH connection to $SERVER_IP..."
    
    if ! command -v sshpass &> /dev/null; then
        print_error "sshpass is not installed. Please install it first:"
        echo "  Ubuntu/Debian: sudo apt-get install sshpass"
        echo "  CentOS/RHEL: sudo yum install sshpass"
        echo "  macOS: brew install sshpass"
        exit 1
    fi
    
    if execute_remote_command "echo 'Connection successful'" "Testing connection"; then
        print_success "SSH connection established successfully"
        return 0
    else
        print_error "Failed to establish SSH connection"
        exit 1
    fi
}

# Check current n8n status
check_n8n_status() {
    print_status "Checking current n8n container status..."
    
    local status_output
    status_output=$(sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" \
        "docker ps -a --filter name=$N8N_CONTAINER_NAME --format 'table {{.Names}}\t{{.Status}}\t{{.Image}}'" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$status_output" ]; then
        echo "$status_output"
        log "Current n8n status: $status_output"
        
        # Get current version
        local current_version
        current_version=$(sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" \
            "docker inspect $N8N_CONTAINER_NAME --format='{{.Config.Image}}' 2>/dev/null" | cut -d':' -f2)
        
        if [ -n "$current_version" ]; then
            print_status "Current n8n version: $current_version"
        fi
        
        return 0
    else
        print_warning "n8n container not found or not accessible"
        return 1
    fi
}

# Create backup
create_backup() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_name="n8n_backup_$timestamp"
    
    print_status "Creating backup: $backup_name"
    
    # Create backup directory
    execute_remote_command "mkdir -p $BACKUP_DIR" "Creating backup directory"
    
    # Stop n8n container gracefully
    execute_remote_command "docker stop $N8N_CONTAINER_NAME" "Stopping n8n container for backup"
    
    # Create container backup
    execute_remote_command "docker commit $N8N_CONTAINER_NAME $backup_name" "Creating container image backup"
    
    # Export container backup
    execute_remote_command "docker save $backup_name > $BACKUP_DIR/${backup_name}.tar" "Exporting container backup"
    
    # Backup volumes (assuming standard n8n setup)
    execute_remote_command "docker run --rm -v n8n_data:/data -v $BACKUP_DIR:/backup alpine tar czf /backup/${backup_name}_data.tar.gz -C /data ." "Backing up n8n data volume"
    
    # Start container back up
    execute_remote_command "docker start $N8N_CONTAINER_NAME" "Restarting n8n container after backup"
    
    print_success "Backup created successfully: $backup_name"
    echo "$backup_name" > /tmp/last_backup_name
}

# Get available n8n versions
get_available_versions() {
    print_status "Fetching available n8n versions..."

    # Get latest 10 versions from Docker Hub API
    local versions
    versions=$(curl -s "https://registry.hub.docker.com/v2/repositories/n8nio/n8n/tags/?page_size=20" | \
        grep -o '"name":"[^"]*"' | \
        grep -v latest | \
        sed 's/"name":"//g' | \
        sed 's/"//g' | \
        grep -E '^[0-9]+\.[0-9]+\.[0-9]+$' | \
        sort -V -r | \
        head -10)

    if [ -n "$versions" ]; then
        echo "Available recent versions:"
        echo "$versions" | nl -w2 -s'. '
        echo "99. latest (most recent)"
        echo "0. Custom version"
        return 0
    else
        print_warning "Could not fetch versions from Docker Hub"
        return 1
    fi
}

# Select n8n version
select_version() {
    echo ""
    print_status "Select n8n version to install:"

    if get_available_versions; then
        echo ""
        echo -n "Enter your choice (number): "
        read -r version_choice

        case $version_choice in
            0)
                echo -n "Enter custom version (e.g., 1.15.0): "
                read -r custom_version
                if [[ $custom_version =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                    N8N_TARGET_VERSION="$custom_version"
                    print_status "Selected custom version: $N8N_TARGET_VERSION"
                else
                    print_error "Invalid version format. Using latest."
                    N8N_TARGET_VERSION="latest"
                fi
                ;;
            99)
                N8N_TARGET_VERSION="latest"
                print_status "Selected version: latest"
                ;;
            [1-9]|1[0-9]|20)
                local versions_array
                mapfile -t versions_array < <(curl -s "https://registry.hub.docker.com/v2/repositories/n8nio/n8n/tags/?page_size=20" | \
                    grep -o '"name":"[^"]*"' | \
                    grep -v latest | \
                    sed 's/"name":"//g' | \
                    sed 's/"//g' | \
                    grep -E '^[0-9]+\.[0-9]+\.[0-9]+$' | \
                    sort -V -r | \
                    head -10)

                local selected_index=$((version_choice - 1))
                if [ $selected_index -ge 0 ] && [ $selected_index -lt ${#versions_array[@]} ]; then
                    N8N_TARGET_VERSION="${versions_array[$selected_index]}"
                    print_status "Selected version: $N8N_TARGET_VERSION"
                else
                    print_error "Invalid selection. Using latest."
                    N8N_TARGET_VERSION="latest"
                fi
                ;;
            *)
                print_error "Invalid selection. Using latest."
                N8N_TARGET_VERSION="latest"
                ;;
        esac
    else
        echo -n "Enter n8n version to install (or 'latest'): "
        read -r manual_version
        N8N_TARGET_VERSION="${manual_version:-latest}"
        print_status "Selected version: $N8N_TARGET_VERSION"
    fi
}

# Update n8n container
update_n8n() {
    print_status "Starting n8n update process to version: $N8N_TARGET_VERSION"

    # Stop current container
    execute_remote_command "docker stop $N8N_CONTAINER_NAME" "Stopping current n8n container"

    # Remove current container (keep volumes)
    execute_remote_command "docker rm $N8N_CONTAINER_NAME" "Removing old container"

    # Pull specified n8n image
    execute_remote_command "docker pull n8nio/n8n:$N8N_TARGET_VERSION" "Pulling n8n image version $N8N_TARGET_VERSION"

    # Start new container with same configuration
    local run_command="docker run -d --name $N8N_CONTAINER_NAME --restart unless-stopped \
        -p 5678:5678 \
        -v n8n_data:/home/<USER>/.n8n \
        -e N8N_HOST=0.0.0.0 \
        -e N8N_PORT=5678 \
        -e WEBHOOK_URL=http://$SERVER_IP:5678/ \
        n8nio/n8n:$N8N_TARGET_VERSION"

    execute_remote_command "$run_command" "Starting updated n8n container"

    # Wait for container to start
    show_progress 10 "Waiting for n8n to start"

    print_success "n8n update to version $N8N_TARGET_VERSION completed"
}

# Verify update
verify_update() {
    print_status "Verifying n8n update..."

    # Check container status
    local container_status
    container_status=$(sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" \
        "docker ps --filter name=$N8N_CONTAINER_NAME --format '{{.Status}}'" 2>/dev/null)

    if [[ "$container_status" == *"Up"* ]]; then
        print_success "Container is running: $container_status"
    else
        print_error "Container is not running properly: $container_status"
        return 1
    fi

    # Check if n8n is responding
    print_status "Testing n8n web interface..."
    local http_status
    http_status=$(sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" \
        "curl -s -o /dev/null -w '%{http_code}' http://localhost:5678 --connect-timeout 10" 2>/dev/null)

    if [ "$http_status" = "200" ] || [ "$http_status" = "302" ]; then
        print_success "n8n web interface is responding (HTTP $http_status)"
    else
        print_warning "n8n web interface returned HTTP $http_status"
    fi

    # Get new version
    local new_version
    new_version=$(sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" \
        "docker inspect $N8N_CONTAINER_NAME --format='{{.Config.Image}}' 2>/dev/null" | cut -d':' -f2)

    if [ -n "$new_version" ]; then
        print_success "Updated to n8n version: $new_version"
    fi

    print_success "Update verification completed"
}

# Rollback function
rollback() {
    local backup_name
    if [ -f /tmp/last_backup_name ]; then
        backup_name=$(cat /tmp/last_backup_name)
    else
        print_error "No backup name found. Cannot rollback."
        return 1
    fi

    print_warning "Starting rollback to backup: $backup_name"

    # Stop current container
    execute_remote_command "docker stop $N8N_CONTAINER_NAME" "Stopping current container"
    execute_remote_command "docker rm $N8N_CONTAINER_NAME" "Removing current container"

    # Restore from backup
    execute_remote_command "docker load < $BACKUP_DIR/${backup_name}.tar" "Loading backup image"
    execute_remote_command "docker run -d --name $N8N_CONTAINER_NAME --restart unless-stopped \
        -p 5678:5678 \
        -v n8n_data:/home/<USER>/.n8n \
        -e N8N_HOST=0.0.0.0 \
        -e N8N_PORT=5678 \
        -e WEBHOOK_URL=http://$SERVER_IP:5678/ \
        $backup_name" "Starting rollback container"

    # Restore data if needed
    execute_remote_command "docker run --rm -v n8n_data:/data -v $BACKUP_DIR:/backup alpine \
        sh -c 'rm -rf /data/* && tar xzf /backup/${backup_name}_data.tar.gz -C /data'" "Restoring data volume"

    print_success "Rollback completed successfully"
}

# Main menu
show_menu() {
    echo ""
    echo "=================================="
    echo "    N8N UPDATE AUTOMATION TOOL"
    echo "=================================="
    echo "1. Test SSH Connection"
    echo "2. Check Current N8N Status"
    echo "3. Create Backup Only"
    echo "4. Select N8N Version to Install"
    echo "5. Full Update Process (Backup + Update + Verify)"
    echo "6. Rollback to Last Backup"
    echo "7. View Update Log"
    echo "8. Exit"
    echo "=================================="
    echo -n "Please select an option (1-8): "
}

# Confirmation prompt
confirm_action() {
    local action=$1
    echo ""
    print_warning "You are about to: $action"
    echo -n "Are you sure you want to continue? (y/N): "
    read -r confirmation
    case $confirmation in
        [yY]|[yY][eE][sS])
            return 0
            ;;
        *)
            print_status "Operation cancelled by user"
            return 1
            ;;
    esac
}

# Full update process
full_update_process() {
    if ! confirm_action "perform a full n8n update (backup + update + verify) to version $N8N_TARGET_VERSION"; then
        return 1
    fi

    print_status "Starting full n8n update process to version: $N8N_TARGET_VERSION"

    # Step 1: Test connection
    if ! test_connection; then
        print_error "Connection test failed. Aborting update."
        return 1
    fi

    # Step 2: Check current status
    check_n8n_status

    # Step 3: Create backup
    if ! create_backup; then
        print_error "Backup creation failed. Aborting update."
        return 1
    fi

    # Step 4: Update n8n
    if ! update_n8n; then
        print_error "Update failed. You may want to rollback."
        return 1
    fi

    # Step 5: Verify update
    if ! verify_update; then
        print_warning "Update verification had issues. Check manually or consider rollback."
        return 1
    fi

    print_success "Full update process completed successfully!"
    print_success "Updated to n8n version: $N8N_TARGET_VERSION"
    print_status "Log file saved to: $LOG_FILE"
}

# Main execution
main() {
    # Create log file
    touch "$LOG_FILE"
    log "N8N Update Automation Script Started"

    # Check dependencies
    if ! command -v sshpass &> /dev/null; then
        print_error "sshpass is required but not installed."
        print_status "Install it with:"
        echo "  Ubuntu/Debian: sudo apt-get install sshpass"
        echo "  CentOS/RHEL: sudo yum install sshpass"
        echo "  macOS: brew install sshpass"
        exit 1
    fi

    while true; do
        show_menu
        read -r choice

        case $choice in
            1)
                test_connection
                ;;
            2)
                check_n8n_status
                ;;
            3)
                if confirm_action "create a backup of the current n8n installation"; then
                    create_backup
                fi
                ;;
            4)
                select_version
                ;;
            5)
                full_update_process
                ;;
            6)
                if confirm_action "rollback to the last backup"; then
                    rollback
                fi
                ;;
            7)
                if [ -f "$LOG_FILE" ]; then
                    print_status "Showing last 20 lines of log file:"
                    tail -20 "$LOG_FILE"
                else
                    print_warning "No log file found"
                fi
                ;;
            8)
                print_status "Exiting n8n update automation tool"
                log "Script ended by user"
                exit 0
                ;;
            *)
                print_error "Invalid option. Please select 1-8."
                ;;
        esac

        echo ""
        echo "Press Enter to continue..."
        read -r
    done
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
