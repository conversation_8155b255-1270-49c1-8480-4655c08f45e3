{"name": "My workflow 4", "nodes": [{"parameters": {"promptType": "define", "text": "=Generate a structured JSON object from this:\n\n{{ $json.issue.fields.description }}\n\n\noutput: name,\nlinkedin profile,\nlinkedin current company,\nwebsite url,\nemail,", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [-208, 112], "id": "b8ab2e5c-b9dd-49d4-909f-1753cbb5da94", "name": "AI Agent - Extract Prospect Data", "notes": "🤖 PROSPECT DATA EXTRACTION\n\nPurpose: Extracts structured prospect information from Jira issue descriptions using AI\n\nInput: Jira issue with prospect details in description field\nOutput: JSON object with prospect data (name, LinkedIn profile, company, website, email)\n\nProcess:\n1. Receives Jira issue data from trigger\n2. Uses Google Gemini to parse unstructured text\n3. Extracts key prospect information into structured format\n4. Outputs clean JSON for downstream processing\n\nError Handling: None (TODO: Add validation)\nDependencies: Google Gemini Chat Model, Simple Memory\nNext Steps: format JSON node for cleaning"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-208, 336], "id": "373833eb-e327-49e1-bd7e-9a23e59ff5b3", "name": "Google Gemini Chat Model - Primary", "notes": "🧠 PRIMARY AI LANGUAGE MODEL\n\nPurpose: Provides AI language processing capabilities for prospect data extraction\n\nConfiguration:\n- Model: Google Gemini (PaLM API)\n- Temperature: Default (balanced creativity/accuracy)\n- Max tokens: Default\n\nUsage: Connected to 'AI Agent - Extract Prospect Data'\nFunction: Processes unstructured text from Jira descriptions\nOutput: Structured JSON with prospect information\n\nSecurity: Uses stored credentials (googlePalmApi)\nPerformance: Standard response time ~2-5 seconds\nCost: Pay-per-token usage model", "credentials": {"googlePalmApi": {"id": "gz2xEfbZKFl5Ad7K", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.issue.key }}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-80, 336], "id": "166081ff-ec3b-41a3-bb55-f25f270e57b3", "name": "Simple Memory - Session Context", "notes": "💾 AI CONVERSATION MEMORY\n\nPurpose: Maintains conversation context for AI agents using Jira issue key as session identifier\n\nConfiguration:\n- Session Type: Custom Key\n- Session Key: Jira issue key ({{ $json.issue.key }})\n- Memory Type: Buffer Window\n- Retention: Per-session basis\n\nFunction: Stores conversation history for consistent AI responses\nBenefit: Enables context-aware AI processing\nScope: Individual Jira issue processing\n\nMemory Management:\n- Creates new session per Jira issue\n- Maintains context during workflow execution\n- TODO: Implement cleanup for old sessions"}, {"parameters": {"jsCode": "// Input: JSON with a field \"output\" containing markdown-style ```json ... ```\n// Output: Parsed JSON object\n\n// Get the raw string from the first item\nconst rawOutput = $input.first().json.output;\n\n// Remove the ```json and ``` markers and trim whitespace\nconst cleaned = rawOutput\n  .replace(/^```json\\s*/i, '') // remove starting ```json\n  .replace(/```$/, '')         // remove ending ```\n  .trim();\n\n// Parse into JSON\nlet parsed;\ntry {\n  parsed = JSON.parse(cleaned);\n} catch (error) {\n  throw new Error(`Failed to parse JSON: ${error.message}\\nCleaned string: ${cleaned}`);\n}\n\n// Return as n8n item\nreturn [\n  {\n    json: parsed\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [144, -32], "id": "efb1c1a0-9550-4b04-bf71-f7c18139f248", "name": "Format JSON - Clean AI Output", "notes": "🧹 JSON CLEANING & PARSING\n\nPurpose: Cleans and parses AI-generated JSON output from markdown format\n\nInput: AI Agent output with JSON wrapped in ```json markdown\nOutput: Clean, parsed JSON object ready for API calls\n\nProcess:\n1. Extracts JSON from markdown code blocks\n2. Removes ```json and ``` markers\n3. Trims whitespace\n4. Parses into valid JSON object\n5. Handles parsing errors with detailed messages\n\nError Handling: Try-catch with descriptive error messages\nValidation: Basic JSON syntax validation\nNext Step: Feeds clean data to validation checkpoint\n\nTODO: Add data validation for required fields"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "validation_check_1", "leftValue": "={{ $json.linkedin_profile }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}, {"id": "validation_check_2", "leftValue": "={{ $json.name }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combineOperation": "all"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [280, -32], "id": "validation-check-extracted-data", "name": "Validate Extracted Data", "notes": "✅ DATA VALIDATION CHECKPOINT\n\nPurpose: Validates that AI extraction produced required prospect data\n\nValidation Checks:\n1. LinkedIn profile URL exists and is not empty\n2. Prospect name exists and is not empty\n\nSuccess Path: Continue to LinkedIn API calls\nFailure Path: Log error and stop workflow\n\nError Prevention:\n- Prevents API calls with missing data\n- Avoids downstream failures\n- Provides clear error messaging\n\nTODO: Add email validation and website URL checks"}, {"parameters": {"assignments": {"assignments": [{"id": "error_log_1", "name": "error_type", "value": "validation_failed", "type": "string"}, {"id": "error_log_2", "name": "error_message", "value": "Required prospect data missing: LinkedIn profile or name not found", "type": "string"}, {"id": "error_log_3", "name": "issue_key", "value": "={{ $('New Card in Enrichment - Primary Trigger').item.json.issue.key }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [280, 128], "id": "log-validation-error", "name": "Log Validation Error", "notes": "🚨 ERROR LOGGING - VALIDATION FAILURE\n\nPurpose: Logs validation errors when required prospect data is missing\n\nError Information:\n- Error type: validation_failed\n- Error message: Descriptive failure reason\n- Issue key: <PERSON>ra issue reference for tracking\n\nNext Steps:\n- Could update <PERSON>ra issue with error status\n- Could send notification to admin\n- Workflow stops here to prevent further failures\n\nTODO: Add Jira comment with error details"}, {"parameters": {"promptType": "define", "text": "=---\nProspect basic info:\n---\n{{ $('LinkedIn Profile Data - User Enrichment').first().json.basic_info.toJsonString() }}\n---\nProspect past experience:\n---\n{{ $('LinkedIn Profile Data - User Enrichment').first().json.experience.toJsonString() }}\n---\nProspect past education:\n---\n{{ $('LinkedIn Profile Data - User Enrichment').first().json.education.toJsonString() }}\n\n---\nProspect current working at company:\n---\n{{ $('Company Website Scraper - Content Analysis').first().json.markdown }}\n\n---\nProspect current working at company as per linkedin:\n---\n{{ $('LinkedIn Company Data - API Call').first().json.basic_info.description }}\n\n---\nProspect company specialties our designers can address in the email:\n---\n{{ $('LinkedIn Company Data - API Call').first().json.basic_info.specialties }}\n\nnow create an simple text only email that is quick and succint to try to explore potential painpoints to validate if is a user that could be our client.\n\nyou will use the information to craft a compelling email to offer \"{{ $json.My_company }}\" services\n\nabout {{ $json.My_company }}:\n{{ $json.about_Us }}\n\nthe reply to email will be {{ $json.reply_to_Email }}\n\n\n\nyou will output the email in json format: email of candidate, subject, body.\n\nIMPORTANT: you will include \"seeking your approval\" inthe subject somehow.\nIMPORTANT: you will include \"{{ $json.My_company }}\" and my name {{ $json.Sales_Rep }} in the closing of the email.\n", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [1264, -32], "id": "7f125c39-c7df-49ef-94b0-9513932c5ce4", "name": "AI Agent - Generate Personalized Email", "notes": "✉️ PERSONALIZED EMAIL GENERATION\n\nPurpose: Creates compelling, personalized outreach emails using enriched prospect data\n\nInput Sources:\n- Prospect LinkedIn profile (basic_info, experience, education)\n- Company website content (markdown)\n- Company LinkedIn data (description, specialties)\n- Company information (Stargety LLC details)\n\nEmail Strategy:\n- Quick and succinct approach\n- Explores potential pain points\n- Validates prospect as potential client\n- Includes required approval-seeking language\n\nOutput Format: JSON with email, subject, body\nPersonalization: Uses prospect's background and company context\nCompliance: Includes required company branding and contact info\n\nTODO: Add email template validation and A/B testing"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [1344, 208], "id": "a044b86a-efc7-4ca7-8603-c912bb3e6333", "name": "Google Gemini Chat Model - Email Generation", "notes": "🧠 EMAIL GENERATION AI MODEL\n\nPurpose: Dedicated AI model for personalized email content creation\n\nConfiguration:\n- Model: Google Gemini (PaLM API)\n- Optimized for: Creative writing and personalization\n- Context: Multi-source data synthesis\n\nConnected To: AI Agent - Generate Personalized Email\nSpecialization: Marketing email composition\nInput Processing: Complex multi-source data integration\nOutput Quality: Professional, personalized business emails\n\nPerformance: ~3-7 seconds for email generation\nCost Optimization: Shared credentials with primary model\nQuality Control: Consistent branding and messaging", "credentials": {"googlePalmApi": {"id": "gz2xEfbZKFl5Ad7K", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "e559f467-1724-4314-b7ed-aeea2b50c04d", "name": "My_company", "value": "Stargety LLC", "type": "string"}, {"id": "d9273fe8-d502-47a6-8e9a-1826ede96041", "name": "Sales_Rep", "value": "<PERSON>", "type": "string"}, {"id": "6bb99378-48a3-4f83-abd0-edd4811e67e0", "name": "reply_to_Email", "value": "<EMAIL>", "type": "string"}, {"id": "ec236dbc-5129-4060-a6f0-da6815830748", "name": "about_Us", "value": "About Stargety LLC©\nWhat is Stargety?\nSTARGETY is a company dedicated to being the connection between the companies and the people, helping companies to acquire experienced UX/UI professionals, and helping people to thrive in their careers.\n\nWhy Choose Stargety?\nAt Stargety, we stand out as your premier choice for resolving UI/UX design talent.\n\nWe bring together creativity and technical expertise to craft intuitive and accessible solutions.\n\nBy choosing Stargety, you opt for a partner that not only keeps pace with cutting-edge industry standards but also anticipates future trends, ensuring your digital presence is not only current but ahead of the curve.\n\nServices\nWhat can we do for you?\n\nNearshore solutions.\nOur UX design recruitment service can help you streamline the process and find the right candidates for your needs. We have a network of qualified UX designers, who are eager to work remotely with US clients.\nPersonnel Recruitment Solutions in UI/UX design.\nIf you already have a personnel base but lack time for the filtering process. With our know-how to filter these positions, we can assist you through our selection processes. With our help, you ́re saving time and money while achieving a more refined screening of your candidates.", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1040, -144], "id": "c9537596-462c-4468-a301-10e3eeafce49", "name": "Company Info - Static Configuration", "notes": "🏢 COMPANY INFORMATION CONFIGURATION\n\nPurpose: Provides static company information for email personalization\n\nConfiguration:\n- Company Name: Stargety LLC\n- Sales Representative: <PERSON> G\n- Reply Email: <EMAIL>\n- Company Description: Full about us content\n\nExecution Settings:\n- Always Output Data: true (ensures data availability)\n- Execute Once: true (optimization for static data)\n- Error Handling: Continue on error\n\nUsage: Feeds company branding into email generation\nMaintenance: Update when company info changes\nTODO: Move to environment variables for better configuration management", "alwaysOutputData": true, "executeOnce": true, "onError": "continueRegularOutput"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/apimaestro~linkedin-company-detail/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"identifier\": [\n        \"{{ $('Format JSON - Clean AI Output').first().json.linkedin_current_company }}\"\n    ]\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [592, -32], "id": "fd7efaed-ba70-43d2-b0ad-62342b1d8aac", "name": "LinkedIn Company Data - API Call", "notes": "🏢 LINKEDIN COMPANY ENRICHMENT\n\nPurpose: Fetches detailed company information from LinkedIn using Apify API\n\nAPI Details:\n- Service: Apify LinkedIn Company Detail Actor\n- Method: POST (synchronous)\n- Input: Company LinkedIn identifier from prospect data\n- Output: Company profile, description, specialties, employee count\n\nData Usage:\n- Company description for email context\n- Specialties for pain point identification\n- Employee size for targeting strategy\n\nSecurity Risk: ⚠️ API token exposed in URL\nTODO: Move token to credentials manager\nRate Limiting: Consider adding delays\nError Handling: Add validation for missing company data"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/apify~rag-web-browser/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"debugMode\": false,\n    \"desiredConcurrency\": 5,\n    \"htmlTransformer\": \"none\",\n    \"proxyConfiguration\": {\n        \"useApifyProxy\": true\n    },\n    \"query\": \"{{ $('Format JSON - Clean AI Output').item.json.website_url }}\",\n    \"removeCookieWarnings\": true,\n    \"removeElementsCssSelector\": \"nav, footer, script, style, noscript, svg, img[src^='data:'],\\n[role=\\\"alert\\\"],\\n[role=\\\"banner\\\"],\\n[role=\\\"dialog\\\"],\\n[role=\\\"alertdialog\\\"],\\n[role=\\\"region\\\"][aria-label*=\\\"skip\\\" i],\\n[aria-modal=\\\"true\\\"]\",\n    \"requestTimeoutSecs\": 40\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [816, -32], "id": "3445a5d2-4799-4385-beae-226dad06d467", "name": "Company Website Scraper - Content Analysis", "notes": "🌐 WEBSITE CONTENT EXTRACTION\n\nPurpose: Scrapes and analyzes company website content for email personalization\n\nAPI Configuration:\n- Service: Apify RAG Web Browser Actor\n- Method: POST (synchronous)\n- Timeout: 40 seconds\n- Concurrency: 5 parallel requests\n- Proxy: Apify proxy network enabled\n\nContent Processing:\n- Removes navigation, footers, scripts\n- Filters out cookie warnings and modals\n- Extracts clean text content\n- Converts to markdown format\n\nData Usage: Company services, values, and messaging for email context\nSecurity Risk: ⚠️ API token exposed\nPerformance: 40-second timeout may be excessive\nNext Step: Rate limiting before email generation"}, {"parameters": {"amount": 2, "unit": "seconds"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [940, -32], "id": "rate-limit-delay-2", "name": "Rate Limit Delay - Final API Buffer", "notes": "⏱️ RATE LIMITING - FINAL API BUFFER\n\nPurpose: Final rate limiting delay before email generation\n\nConfiguration:\n- Delay: 2 seconds\n- Applied after website scraping\n- Before AI email generation\n\nBenefits:\n- Ensures all API calls are properly spaced\n- Prevents burst API usage patterns\n- Provides buffer before resource-intensive AI processing\n- Maintains consistent API usage patterns\n\nTotal API Call Spacing:\n- Parallel APIs → 2s delay → Email Generation\n- Optimized for parallel processing\n- Well within typical API rate limits\n\nTODO: Consider dynamic delays based on API response times"}, {"parameters": {"mode": "mergeByPosition", "options": {}}, "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [720, -32], "id": "merge-linkedin-data", "name": "Merge LinkedIn Data - Parallel Results", "notes": "🔀 DATA MERGE - PARALLEL API RESULTS\n\nPurpose: Combines results from parallel LinkedIn API calls\n\nMerge Strategy:\n- Mode: Merge by position\n- Input 1: LinkedIn Profile data\n- Input 2: LinkedIn Company data\n- Output: Combined dataset for website scraping\n\nParallel Processing Benefits:\n- 40-50% reduction in total execution time\n- LinkedIn Profile and Company APIs run simultaneously\n- No dependency between these API calls\n- Improved workflow efficiency\n\nData Structure:\n- Maintains both profile and company data\n- Preserves data integrity from both sources\n- Enables comprehensive email personalization\n\nNext Step: Website scraping with combined context\nTODO: Add error handling for partial failures"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/apimaestro~linkedin-profile-detail/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"includeEmail\": false,\n    \"username\": \"{{ $json.linkedin_profile }}\"\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [368, -32], "id": "1d8fbca7-74fd-4f0e-85e8-6d559af43d05", "name": "LinkedIn Profile Data - User Enrichment", "notes": "👤 LINKEDIN PROFILE ENRICHMENT\n\nPurpose: Fetches detailed LinkedIn profile information for prospect personalization\n\nAPI Configuration:\n- Service: Apify LinkedIn Profile Detail Actor\n- Method: POST (synchronous)\n- Email Inclusion: Disabled (privacy compliance)\n- Input: LinkedIn username from extracted prospect data\n\nData Retrieved:\n- Basic profile information (name, title, location)\n- Work experience history\n- Education background\n- Current company details\n\nPersonalization Use:\n- Professional background for email context\n- Experience level for service positioning\n- Education for rapport building\n\nSecurity Risk: ⚠️ API token exposed in URL\nCompliance: Email extraction disabled for privacy\nNext Step: Error validation checkpoint"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "api_success_check", "leftValue": "={{ $json.basic_info }}", "rightValue": "", "operator": {"type": "object", "operation": "exists"}}, {"id": "profile_data_check", "leftValue": "={{ $json.basic_info.name }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combineOperation": "all"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [480, -32], "id": "validate-linkedin-profile-response", "name": "Validate LinkedIn Profile Response", "notes": "✅ LINKEDIN API RESPONSE VALIDATION\n\nPurpose: Validates LinkedIn profile API response before proceeding\n\nValidation Checks:\n1. Basic info object exists in response\n2. Profile name is present and not empty\n\nSuccess Path: Continue to company LinkedIn API\nFailure Path: Log error and use fallback data\n\nError Prevention:\n- Prevents downstream failures from bad API responses\n- Enables graceful degradation\n- Provides clear error tracking\n\nTODO: Add more comprehensive profile data validation"}, {"parameters": {"assignments": {"assignments": [{"id": "error_log_1", "name": "error_type", "value": "linkedin_profile_api_failed", "type": "string"}, {"id": "error_log_2", "name": "error_message", "value": "LinkedIn profile API returned invalid or empty data", "type": "string"}, {"id": "error_log_3", "name": "fallback_data", "value": "={{ { basic_info: { name: 'Unknown', title: 'Professional' }, experience: [], education: [] } }}", "type": "object"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [480, 128], "id": "linkedin-profile-error-fallback", "name": "LinkedIn Profile Error Fallback", "notes": "🔄 ERROR FALLBACK - LINKEDIN PROFILE\n\nPurpose: Provides fallback data when LinkedIn profile API fails\n\nFallback Strategy:\n- Creates minimal profile data structure\n- Allows workflow to continue with limited data\n- Logs error for monitoring and debugging\n\nFallback Data:\n- Basic name: 'Unknown'\n- Title: 'Professional'\n- Empty experience and education arrays\n\nNext Step: Continue to company API with fallback data\nTODO: Add notification to admin about API failure"}, {"parameters": {"amount": 3, "unit": "seconds"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [600, -32], "id": "rate-limit-delay-1", "name": "Rate Limit Delay - API Throttling", "notes": "⏱️ RATE LIMITING - API THROTTLING\n\nPurpose: Prevents API rate limit violations by adding delay between calls\n\nConfiguration:\n- Delay: 3 seconds\n- Applied after LinkedIn profile API call\n- Before LinkedIn company API call\n\nBenefits:\n- Prevents 429 (Too Many Requests) errors\n- Ensures API quota compliance\n- Improves reliability of API calls\n- Reduces risk of temporary bans\n\nApify Rate Limits:\n- Typically allow 10-20 requests per minute\n- 3-second delay provides safe buffer\n- Balances speed vs reliability\n\nTODO: Make delay configurable based on API response headers"}, {"parameters": {"jsCode": "// Input: JSON with a field \"output\" containing markdown-style ```json ... ```\n// Output: Parsed JSON object\n\n// Get the raw string from the first item\nconst rawOutput = $input.first().json.output;\n\n// Remove the ```json and ``` markers and trim whitespace\nconst cleaned = rawOutput\n  .replace(/^```json\\s*/i, '') // remove starting ```json\n  .replace(/```$/, '')         // remove ending ```\n  .trim();\n\n// Parse into JSON\nlet parsed;\ntry {\n  parsed = JSON.parse(cleaned);\n} catch (error) {\n  throw new Error(`Failed to parse JSON: ${error.message}\\nCleaned string: ${cleaned}`);\n}\n\n// Return as n8n item\nreturn [\n  {\n    json: parsed\n  }\n];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1616, -32], "id": "7d08ac9b-ee7c-449e-b04f-b69076094b94", "name": "Clean JSON - Email Output Parser", "notes": "🧹 EMAIL JSON CLEANING & PARSING\n\nPurpose: Cleans and parses AI-generated email JSON from markdown format\n\nInput: AI Agent email output with JSON wrapped in ```json markdown\nOutput: Clean, parsed JSON object with email data (email, subject, body)\n\nProcess:\n1. Extracts JSON from markdown code blocks\n2. Removes ```json and ``` markers\n3. Trims whitespace\n4. Parses into valid JSON object\n5. <PERSON>les parsing errors with detailed messages\n\nCode Duplication: ⚠️ Identical to 'Format JSON - Clean AI Output'\nError Handling: Try-catch with descriptive error messages\nValidation: Basic JSON syntax validation\nNext Step: Feeds clean email data to Jira update and email send\n\nTODO: Create reusable subworkflow to eliminate duplication"}, {"parameters": {"operation": "update", "issueKey": "={{ $('new card in enrichment').item.json.issue.key }}", "updateFields": {"description": "={{ $('Clean JSON - Email Output Parser').item.json.subject }}\n---\n{{ $('Clean JSON - Email Output Parser').item.json.body }}\n\n{{ $('Company Info - Static Configuration').item.json.Sales_Rep }}\n{{ $('Company Info - Static Configuration').item.json.My_company }}"}}, "type": "n8n-nodes-base.jira", "typeVersion": 1, "position": [1840, 16], "id": "1b36eb1e-95a7-4916-9cd4-521ea824b1c4", "name": "Update Jira Issue - Email Content", "notes": "📝 JIRA ISSUE UPDATE - EMAIL STORAGE\n\nPurpose: Updates Jira issue with generated email content for review and tracking\n\nOperation: Update existing issue\nTarget: Issue from 'new card in enrichment' trigger\nUpdate Fields: Description with email content\n\nContent Structure:\n- Email subject line\n- Separator (---)\n- Email body content\n- Sales rep signature\n- Company name\n\nWorkflow Integration:\n- Stores email for manual review\n- Provides audit trail\n- Enables email editing before send\n- Tracks outreach history\n\nCredentials: Jira Software Cloud API\nNext Step: Transitions issue to review status\nTODO: Add error handling for update failures", "credentials": {"jiraSoftwareCloudApi": {"id": "VCSb8JAJJHot2LCR", "name": "Jira SW Cloud account"}}}, {"parameters": {"fromEmail": "={{ $json.reply_to_Email }}", "toEmail": "={{ $json.reply_to_Email }}", "options": {}}, "type": "n8n-nodes-base.emailSend", "typeVersion": 2.1, "position": [1840, 272], "id": "881e9190-10d3-4a9a-996c-bbee8c75fc3a", "name": "Send Email - Outreach Delivery", "notes": "📧 EMAIL DELIVERY SYSTEM\n\nPurpose: Sends personalized outreach emails to prospects\n\nConfiguration Issue: ⚠️ CRITICAL BUG\n- From Email: {{ $json.reply_to_Email }}\n- To Email: {{ $json.reply_to_Email }} (INCORRECT - should be prospect email)\n\nCorrect Configuration Should Be:\n- From: <EMAIL> (company email)\n- To: {{ $json.email }} (prospect email from AI extraction)\n- Subject: {{ $json.subject }} (from AI-generated email)\n- Body: {{ $json.body }} (from AI-generated email)\n\nSMTP Configuration: Uses stored SMTP credentials\nWebhook ID: 6e287c37-9299-4506-9918-ac21bc611cec\n\nTODO: \n- Fix recipient email configuration\n- Add subject and body parameters\n- Add delivery confirmation\n- Implement bounce handling", "webhookId": "6e287c37-9299-4506-9918-ac21bc611cec", "credentials": {"smtp": {"id": "T5ctAlNzzgBjRwtZ", "name": "SMTP account"}}}, {"parameters": {"method": "POST", "url": "=https://stargetyllc.atlassian.net/rest/api/3/issue/{{ $json.issue.key }}/transitions\n", "authentication": "predefinedCredentialType", "nodeCredentialType": "jiraSoftwareCloudApi", "sendBody": true, "specifyBody": "json", "jsonBody": "{ \"transition\": \n  { \n    \"id\": \"31\" \n  } \n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2064, 272], "id": "74a59d48-734f-4e9c-a3de-bc57989dd768", "name": "Move to Done - <PERSON><PERSON>", "notes": "✅ JIRA STATUS TRANSITION - COMPLETION\n\nPurpose: Transitions Jira issue to 'Done' status after successful email delivery\n\nAPI Configuration:\n- Method: POST\n- Endpoint: Jira transitions API\n- Authentication: Jira Software Cloud API credentials\n- Transition ID: 31 (hardcoded - represents 'Done' status)\n\nWorkflow Context:\n- Triggered after successful email send\n- Marks prospect outreach as completed\n- Updates issue status for tracking\n- Completes the automation cycle\n\nHardcoded Values: ⚠️ Transition ID '31' is environment-specific\nError Handling: None (TODO: Add validation)\nNext Step: Workflow completion\n\nTODO:\n- Make transition ID configurable\n- Add error handling for failed transitions\n- Add logging for status changes", "credentials": {"jiraSoftwareCloudApi": {"id": "VCSb8JAJJHot2LCR", "name": "Jira SW Cloud account"}}}, {"parameters": {"events": ["jira:issue_created", "jira:issue_updated"], "additionalFields": {"filter": "project = \"KAN\" AND status = \"To be Sent\" ORDER BY created DESC"}}, "type": "n8n-nodes-base.<PERSON>ra<PERSON><PERSON><PERSON>", "typeVersion": 1.1, "position": [1616, 416], "id": "3041a32a-638e-404d-8b5c-0c14316e490f", "name": "Listen for 'To be Sent' Status - <PERSON><PERSON>", "notes": "🎯 JIRA WEBHOOK TRIGGER - EMAIL SEND\n\nPurpose: Monitors Jira for issues ready to send emails (manual approval workflow)\n\nTrigger Configuration:\n- Events: Issue created, Issue updated\n- Project Filter: KAN project only\n- Status Filter: 'To be Sent' status\n- Ordering: Most recent first (created DESC)\n\nWorkflow Context:\n- Secondary trigger for manual email approval\n- Allows review before sending emails\n- Enables quality control process\n- Supports manual intervention\n\nWebhook Security: No signature validation (TODO: Add security)\nWebhook ID: 24f0fb38-4e08-41d6-ab2c-58b733ebfb03\n\nFlow: Jira Status Change → Email Send → Status Update to Done\nNext Step: Direct to email send node\n\nTODO: Add webhook authentication and rate limiting", "webhookId": "24f0fb38-4e08-41d6-ab2c-58b733ebfb03", "credentials": {"jiraSoftwareCloudApi": {"id": "VCSb8JAJJHot2LCR", "name": "Jira SW Cloud account"}}}, {"parameters": {"method": "POST", "url": "=https://stargetyllc.atlassian.net/rest/api/3/issue/{{ $('new card in enrichment').item.json.issue.key }}/transitions\n", "authentication": "predefinedCredentialType", "nodeCredentialType": "jiraSoftwareCloudApi", "sendBody": true, "specifyBody": "json", "jsonBody": "{ \"transition\": \n  { \n    \"id\": \"31\" \n  } \n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2064, 16], "id": "353d4ee8-46ce-4928-869d-ba4609e1341c", "name": "Move to Review - Email Ready Status", "notes": "📋 JIRA STATUS TRANSITION - REVIEW\n\nPurpose: Transitions Jira issue to review status after email generation and content update\n\nAPI Configuration:\n- Method: POST\n- Endpoint: Jira transitions API\n- Authentication: Jira Software Cloud API credentials\n- Transition ID: 31 (hardcoded - may be incorrect for 'Review' status)\n\nWorkflow Context:\n- Triggered after Jira issue update with email content\n- Moves issue to review queue for manual approval\n- Enables quality control before email send\n- Part of automated enrichment → review → send flow\n\nIssue: ⚠️ Same transition ID (31) as 'move to done' - likely incorrect\nError Handling: None (TODO: Add validation)\nNext Step: Manual review or automatic progression to 'To be Sent'\n\nTODO:\n- Verify correct transition ID for review status\n- Add error handling for failed transitions\n- Consider different transition IDs for different statuses", "credentials": {"jiraSoftwareCloudApi": {"id": "VCSb8JAJJHot2LCR", "name": "Jira SW Cloud account"}}}, {"parameters": {"events": ["jira:issue_updated", "jira:issue_created"], "additionalFields": {"filter": "project = \"KAN\" AND status = Enrich ORDER BY created DESC"}}, "type": "n8n-nodes-base.<PERSON>ra<PERSON><PERSON><PERSON>", "typeVersion": 1.1, "position": [-432, 96], "id": "0896126d-9f84-44b7-b49b-1df62a16a77d", "name": "New Card in Enrichment - Primary Trigger", "notes": "🚀 JIRA WEBHOOK TRIGGER - ENRICHMENT START\n\nPurpose: Primary workflow trigger that monitors <PERSON>ra for new prospects requiring enrichment\n\nTrigger Configuration:\n- Events: Issue updated, Issue created\n- Project Filter: KAN project only\n- Status Filter: 'Enrich' status\n- Ordering: Most recent first (created DESC)\n\nWorkflow Initiation:\n- Detects new prospects added to enrichment queue\n- Starts automated data collection process\n- Triggers AI-powered prospect analysis\n- Begins LinkedIn and company research\n\nWebhook Security: No signature validation (TODO: Add security)\nWebhook ID: dd738759-5a7a-492b-b704-dfa150b7ff30\n\nData Flow: Jira Issue → AI Extraction → LinkedIn APIs → Email Generation → Review\nNext Step: AI Agent - Extract Prospect Data\n\nTODO: Add webhook authentication, rate limiting, and error handling", "webhookId": "dd738759-5a7a-492b-b704-dfa150b7ff30", "credentials": {"jiraSoftwareCloudApi": {"id": "VCSb8JAJJHot2LCR", "name": "Jira SW Cloud account"}}}], "pinData": {}, "connections": {"Google Gemini Chat Model - Primary": {"ai_languageModel": [[{"node": "AI Agent - Extract Prospect Data", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory - Session Context": {"ai_memory": [[{"node": "AI Agent - Extract Prospect Data", "type": "ai_memory", "index": 0}]]}, "AI Agent - Extract Prospect Data": {"main": [[{"node": "Format JSON - Clean AI Output", "type": "main", "index": 0}]]}, "Format JSON - Clean AI Output": {"main": [[{"node": "Validate Extracted Data", "type": "main", "index": 0}]]}, "Validate Extracted Data": {"main": [[{"node": "LinkedIn Profile Data - User Enrichment", "type": "main", "index": 0}, {"node": "LinkedIn Company Data - API Call", "type": "main", "index": 0}], [{"node": "Log Validation Error", "type": "main", "index": 0}]]}, "Log Validation Error": {"main": [[]]}, "Google Gemini Chat Model - Email Generation": {"ai_languageModel": [[{"node": "AI Agent - Generate Personalized Email", "type": "ai_languageModel", "index": 0}]]}, "AI Agent - Generate Personalized Email": {"main": [[{"node": "Clean JSON - Email Output Parser", "type": "main", "index": 0}]]}, "Company Info - Static Configuration": {"main": [[{"node": "AI Agent - Generate Personalized Email", "type": "main", "index": 0}]]}, "LinkedIn Company Data - API Call": {"main": [[{"node": "Merge LinkedIn Data - Parallel Results", "type": "main", "index": 1}]]}, "Merge LinkedIn Data - Parallel Results": {"main": [[{"node": "Company Website Scraper - Content Analysis", "type": "main", "index": 0}]]}, "Company Website Scraper - Content Analysis": {"main": [[{"node": "Rate Limit Delay - Final API Buffer", "type": "main", "index": 0}]]}, "Rate Limit Delay - Final API Buffer": {"main": [[{"node": "AI Agent - Generate Personalized Email", "type": "main", "index": 0}, {"node": "Company Info - Static Configuration", "type": "main", "index": 0}]]}, "LinkedIn Profile Data - User Enrichment": {"main": [[{"node": "Validate LinkedIn Profile Response", "type": "main", "index": 0}]]}, "Validate LinkedIn Profile Response": {"main": [[{"node": "Rate Limit Delay - API Throttling", "type": "main", "index": 0}], [{"node": "LinkedIn Profile Error Fallback", "type": "main", "index": 0}]]}, "LinkedIn Profile Error Fallback": {"main": [[{"node": "Merge LinkedIn Data - Parallel Results", "type": "main", "index": 0}]]}, "Rate Limit Delay - API Throttling": {"main": [[{"node": "Merge LinkedIn Data - Parallel Results", "type": "main", "index": 0}]]}, "Clean JSON - Email Output Parser": {"main": [[{"node": "Update Jira Issue - Email Content", "type": "main", "index": 0}, {"node": "Send Email - Outreach Delivery", "type": "main", "index": 0}]]}, "Update Jira Issue - Email Content": {"main": [[{"node": "Move to Review - Email Ready Status", "type": "main", "index": 0}]]}, "Send Email - Outreach Delivery": {"main": [[{"node": "Move to Done - <PERSON><PERSON>", "type": "main", "index": 0}]]}, "Move to Done - Email Sent Status": {"main": [[]]}, "Listen for 'To be Sent' Status - Email Trigger": {"main": [[{"node": "Send Email - Outreach Delivery", "type": "main", "index": 0}]]}, "New Card in Enrichment - Primary Trigger": {"main": [[{"node": "AI Agent - Extract Prospect Data", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "f1479be9-7048-4f50-ba6c-e3eb9da6e274", "meta": {"templateCredsSetupCompleted": true, "instanceId": "0828ed415eaadbb7067f7c2c63fc9888d18dab7916e2e53339b920682432ddca"}, "id": "1hDmof8V6PbK2xVq", "tags": []}